<template>
  <div class="goods-header">
    <div class="header">
      <div
        ref="type"
        v-for="(item, index) in typeList"
        :key="index"
        @click="chooseOne(index)"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['typeList'],
  data () {
    return {}
  },
  methods: {
    chooseOne (index) {
      this.$emit('switchTabs', this.typeList[index].id)
      for (let i = 0; i < this.typeList.length; i++) {
        this.$refs.type[i].className = ''
      }
      this.$refs.type[index].className = 'active'
    }
  },
  mounted () {
    this.$refs.type[0].className = 'active'
  }
}
</script>

<style lang="less" scoped>
.goods-header {
  width: 750px;
  position: relative;
  padding-bottom: 20px;
  overflow-x: hidden;
  z-index: 98;
  .header {
    font-size: 28px;
    height: 50px;
    margin-top: 30px;
    display: flex;
    overflow-x: scroll;
    div {
      white-space: nowrap;
      height: 28px;
      line-height: 28px;
      padding: 0 34px;
      transition: 0.3s;
      &:not(:last-child) {
        border-right: 1px solid #d8d8d8;
      }
    }

    .active {
      position: relative;
      z-index: 2;

      &::after {
        content: "";
        display: block;
        position: absolute;
        left: 50%;
        bottom: -2px;
        width: 112px;
        height: 12px;
        border-radius: 10px;
        z-index: -1;
        transform: translateX(-50%);
        background-image: linear-gradient(to right, #ff780a, #ffffff);
        background-color: #ff780a;
        transition: .3s;
      }
    }
  }
}
</style>
